import { SelectionRect } from "@/components/SelectionOverlay";

/**
 * Crops a base64 image data URL based on selection coordinates
 * @param imageDataUrl - The original base64 image data URL
 * @param selection - The selection rectangle coordinates
 * @param displayWidth - The width of the displayed image element
 * @param displayHeight - The height of the displayed image element
 * @returns Promise<string> - The cropped image as a base64 data URL
 */
export async function cropImageFromDataUrl(
  imageDataUrl: string,
  selection: SelectionRect,
  displayWidth: number,
  displayHeight: number
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      try {
        // Calculate scaling factors between displayed image and original image
        const scaleX = img.naturalWidth / displayWidth;
        const scaleY = img.naturalHeight / displayHeight;
        
        // Scale selection coordinates to match original image dimensions
        const scaledSelection = {
          x: selection.x * scaleX,
          y: selection.y * scaleY,
          width: selection.width * scaleX,
          height: selection.height * scaleY,
        };
        
        // Ensure selection is within image bounds
        const clampedSelection = {
          x: Math.max(0, Math.min(scaledSelection.x, img.naturalWidth)),
          y: Math.max(0, Math.min(scaledSelection.y, img.naturalHeight)),
          width: Math.min(scaledSelection.width, img.naturalWidth - scaledSelection.x),
          height: Math.min(scaledSelection.height, img.naturalHeight - scaledSelection.y),
        };
        
        // Create canvas for cropping
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        
        if (!ctx) {
          reject(new Error("Failed to get canvas context"));
          return;
        }
        
        // Set canvas dimensions to match cropped area
        canvas.width = clampedSelection.width;
        canvas.height = clampedSelection.height;
        
        // Draw the cropped portion of the image
        ctx.drawImage(
          img,
          clampedSelection.x,
          clampedSelection.y,
          clampedSelection.width,
          clampedSelection.height,
          0,
          0,
          clampedSelection.width,
          clampedSelection.height
        );
        
        // Convert canvas to base64 data URL
        const croppedDataUrl = canvas.toDataURL("image/jpeg", 0.9);
        resolve(croppedDataUrl);
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => {
      reject(new Error("Failed to load image"));
    };
    
    img.src = imageDataUrl;
  });
}

/**
 * Gets the natural dimensions of an image from a data URL
 * @param imageDataUrl - The base64 image data URL
 * @returns Promise<{width: number, height: number}> - The natural dimensions
 */
export async function getImageDimensions(imageDataUrl: string): Promise<{width: number, height: number}> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };
    
    img.onerror = () => {
      reject(new Error("Failed to load image"));
    };
    
    img.src = imageDataUrl;
  });
}

/**
 * Calculates the display dimensions of an image element
 * @param imageElement - The HTML image element
 * @returns {width: number, height: number} - The display dimensions
 */
export function getDisplayDimensions(imageElement: HTMLImageElement): {width: number, height: number} {
  const rect = imageElement.getBoundingClientRect();
  return {
    width: rect.width,
    height: rect.height,
  };
}

/**
 * Validates if a selection is valid for cropping
 * @param selection - The selection rectangle
 * @param minWidth - Minimum width for valid selection (default: 10)
 * @param minHeight - Minimum height for valid selection (default: 10)
 * @returns boolean - Whether the selection is valid
 */
export function isValidSelection(
  selection: SelectionRect | null,
  minWidth: number = 10,
  minHeight: number = 10
): selection is SelectionRect {
  return (
    selection !== null &&
    selection.width >= minWidth &&
    selection.height >= minHeight
  );
}
