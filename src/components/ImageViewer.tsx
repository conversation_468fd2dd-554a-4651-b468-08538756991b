"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Crop, X } from "lucide-react";
import Image from "next/image";
import SelectionOverlay, { SelectionRect } from "@/components/SelectionOverlay";
import { useState, useRef, useCallback } from "react";
import { cropImageFromDataUrl, getDisplayDimensions } from "@/lib/imageCrop";

interface ImageViewerProps {
  images: string[];
  currentPage: number;
  onPageChange: (page: number) => void;
  onRegionAnalyze?: (croppedImageData: string) => void;
  isAnalyzing?: boolean;
}

export default function ImageViewer({
  images,
  currentPage,
  onPageChange,
  onRegionAnalyze,
  isAnalyzing = false,
}: ImageViewerProps) {
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [currentSelection, setCurrentSelection] =
    useState<SelectionRect | null>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleSelectionChange = useCallback(
    (selection: SelectionRect | null) => {
      setCurrentSelection(selection);
    },
    []
  );

  const handleSelectionComplete = useCallback(
    async (selection: SelectionRect) => {
      if (!onRegionAnalyze || !imageRef.current) return;

      try {
        const displayDimensions = getDisplayDimensions(imageRef.current);
        const croppedImageData = await cropImageFromDataUrl(
          images[currentPage],
          selection,
          displayDimensions.width,
          displayDimensions.height
        );

        onRegionAnalyze(croppedImageData);
      } catch (error) {
        console.error("Failed to crop image:", error);
      }
    },
    [onRegionAnalyze, images, currentPage]
  );

  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    if (isSelectionMode) {
      setCurrentSelection(null);
    }
  };

  const clearSelection = () => {
    setCurrentSelection(null);
  };

  if (!images.length) return null;

  const handlePrevious = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1);
      // Clear selection when changing pages
      setCurrentSelection(null);
      setIsSelectionMode(false);
    }
  };

  const handleNext = () => {
    if (currentPage < images.length - 1) {
      onPageChange(currentPage + 1);
      // Clear selection when changing pages
      setCurrentSelection(null);
      setIsSelectionMode(false);
    }
  };

  return (
    <div className="bg-gray-50 p-6 rounded-lg space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-bold text-lg">PDF プレビュー</h3>
        <div className="text-sm text-gray-600">
          {currentPage + 1} / {images.length} ページ
        </div>
      </div>

      {/* Selection controls */}
      <div className="flex items-center gap-2 justify-center">
        <Button
          variant="outline"
          size="sm"
          onClick={toggleSelectionMode}
          className={`flex items-center gap-1 ${
            isSelectionMode ? "bg-blue-100 border-blue-300" : ""
          }`}
        >
          <Crop className="w-4 h-4" />
          {isSelectionMode ? "選択モード終了" : "範囲選択"}
        </Button>

        {currentSelection && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearSelection}
            className="flex items-center gap-1"
          >
            <X className="w-4 h-4" />
            選択解除
          </Button>
        )}

        {currentSelection && onRegionAnalyze && (
          <Button
            size="sm"
            onClick={() => handleSelectionComplete(currentSelection)}
            disabled={isAnalyzing}
            className="flex items-center gap-1"
          >
            {isAnalyzing ? "解析中..." : "選択範囲を解析"}
          </Button>
        )}
      </div>

      <div className="relative">
        <SelectionOverlay
          isSelectionMode={isSelectionMode}
          onSelectionChange={handleSelectionChange}
          onSelectionComplete={handleSelectionComplete}
          className="w-full max-w-2xl mx-auto"
        >
          <Image
            ref={imageRef}
            src={images[currentPage]}
            alt={`PDF Page ${currentPage + 1}`}
            width={800}
            height={800}
            className="w-full border border-gray-200 rounded shadow-sm"
          />
        </SelectionOverlay>
      </div>

      <div className="flex justify-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleNext}
          disabled={currentPage === images.length - 1}
          className="flex items-center gap-1"
        >
          <ChevronLeft className="w-4 h-4" />
          次のページ
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevious}
          disabled={currentPage === 0}
          className="flex items-center gap-1"
        >
          前のページ
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
