"use client";

import { Analysis } from "@/app/api/recognize/route";
import React, { useState, useEffect } from "react";
import { X, GripVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rollBar } from "@/components/ui/scroll-area";
import {
  Card,
  CardAction,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface Script {
  speaker: string;
  lines: string;
}

interface ResultViewerProps {
  analysis: Analysis | null;
  onAnalysisUpdate?: (updatedAnalysis: Analysis) => void;
}

export default function ResultViewer({
  analysis,
  onAnalysisUpdate,
}: ResultViewerProps) {
  const [scripts, setScripts] = useState<Script[]>(analysis?.scripts || []);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  // Update local state when analysis changes
  useEffect(() => {
    setScripts(analysis?.scripts || []);
  }, [analysis]);

  if (!analysis) return null;

  // // Function to wrap text at 24 characters
  // const wrapText = (text: string, maxLength: number = 24): string => {
  //   if (text.length <= maxLength) return text;

  //   const lines = [];
  //   for (let i = 0; i < text.length; i += maxLength) {
  //     lines.push(text.slice(i, i + maxLength));
  //   }
  //   return lines.join("\n");
  // };

  const handleDragStart = (
    e: React.DragEvent<HTMLDivElement>,
    index: number
  ) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/html", e.currentTarget.outerHTML);
    (e.currentTarget as HTMLElement).style.opacity = "0.5";
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    (e.currentTarget as HTMLElement).style.opacity = "1";
    setDraggedIndex(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const newScripts = [...scripts];
    const draggedScript = newScripts[draggedIndex];

    // Remove dragged item
    newScripts.splice(draggedIndex, 1);

    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newScripts.splice(insertIndex, 0, draggedScript);

    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ scripts: newScripts });
    }
  };

  const handleDelete = (index: number) => {
    const newScripts = scripts.filter((_, i) => i !== index);
    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ scripts: newScripts });
    }
  };

  const handleLinesChange = (index: number, newLines: string) => {
    // Remove existing line breaks and wrap at 24 characters
    const cleanText = newLines.replace(/\n/g, "");
    //const wrappedText = wrapText(cleanText);

    const newScripts = [...scripts];
    newScripts[index] = { ...newScripts[index], lines: cleanText };
    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ scripts: newScripts });
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 to-gray-100 p-8 rounded-xl shadow-inner space-y-6 mt-8">
      <div>
        <h3 className="font-bold text-xl text-gray-800 mb-2">台本</h3>
        <p className="text-sm text-gray-600 mb-6">
          カードをドラッグして並び替え、テキストをクリックして編集
        </p>
        <ScrollArea className="rounded-xl border-2 border-gray-200/50 bg-gradient-to-r from-white to-gray-50 shadow-lg whitespace-nowrap">
          <div className="flex flex-row-reverse w-max space-x-6 space-x-reverse p-6 bg-transparent">
            {scripts.map((script, index) => (
              <Card
                key={index}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
                className="relative bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/20 border-2 border-gray-200/60 hover:border-blue-300/70 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 cursor-move group flex-shrink-0 hover:scale-[1.02] hover:-translate-y-1 backdrop-blur-sm"
                style={{
                  width: "auto",
                  minWidth: "140px",
                  maxWidth: "200px",
                }}
              >
                <CardHeader className="pb-4">
                  <CardTitle
                    className="font-bold text-base tracking-wide bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mr-2 mt-20"
                    style={{
                      writingMode: "vertical-rl",
                      textOrientation: "upright",
                      direction: "ltr",
                    }}
                  >
                    {script.speaker}
                  </CardTitle>
                  <CardAction>
                    {/* Drag handle */}
                    <div className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:scale-110">
                      <div className="bg-gray-100 hover:bg-blue-100 rounded-lg p-2 shadow-sm hover:shadow-md transition-all duration-200">
                        <GripVertical className="w-4 h-4 text-gray-500 hover:text-blue-600" />
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(index)}
                      className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-200 w-8 h-8 p-0 bg-red-50 hover:bg-red-100 border-red-200 hover:border-red-300 hover:scale-110 shadow-sm hover:shadow-md"
                    >
                      <X className="w-4 h-4 text-red-500 hover:text-red-600" />
                    </Button>
                  </CardAction>
                </CardHeader>
                <CardContent className="pt-2">
                  <Textarea
                    value={script.lines}
                    onChange={(e) => handleLinesChange(index, e.target.value)}
                    className="text-gray-700 hover:text-gray-800 text-sm leading-relaxed flex-1 resize-none border-none bg-transparent hover:bg-blue-50/30 focus:bg-blue-50/50 p-4 shadow-none focus-visible:ring-2 focus-visible:ring-blue-300/50 focus-visible:border-none whitespace-pre-line rounded-lg transition-all duration-200 h-86"
                    style={{
                      writingMode: "vertical-rl",
                      textOrientation: "upright",
                      minHeight: "180px",
                      lineHeight: "1.6",
                    }}
                    placeholder="セリフを入力..."
                  />
                </CardContent>
                <CardFooter></CardFooter>
              </Card>
            ))}
          </div>
          <ScrollBar
            orientation="horizontal"
            className="h-3 bg-gray-100 rounded-full"
          />
        </ScrollArea>
      </div>
    </div>
  );
}
