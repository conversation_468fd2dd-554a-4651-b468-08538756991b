"use client";

import { Analysis } from "@/app/api/recognize/route";
import { SelectionRect } from "@/components/SelectionOverlay";

interface RegionAnalysis {
  analysis: Analysis;
  selection: SelectionRect;
  timestamp: number;
}

type CombinedScript = {
  speaker: string;
  lines: string;
} & (
  | { source: "full-page" }
  | { source: "region"; regionIndex: number; selection: SelectionRect }
);

interface ResultViewerProps {
  analysis: Analysis | null;
  regionAnalyses?: RegionAnalysis[];
}

export default function ResultViewer({
  analysis,
  regionAnalyses = [],
}: ResultViewerProps) {
  if (!analysis && (!regionAnalyses || regionAnalyses.length === 0))
    return null;

  // Combine full-page analysis scripts with region analysis scripts
  const allScripts: CombinedScript[] = [];

  // Add full-page analysis scripts first
  if (analysis) {
    allScripts.push(
      ...analysis.scripts.map(
        (script): CombinedScript => ({
          ...script,
          source: "full-page" as const,
        })
      )
    );
  }

  // Add region analysis scripts
  regionAnalyses.forEach((regionAnalysis, regionIndex) => {
    regionAnalysis.analysis.scripts.forEach((script) => {
      allScripts.push({
        ...script,
        source: "region" as const,
        regionIndex: regionIndex + 1,
        selection: regionAnalysis.selection,
      });
    });
  });

  if (allScripts.length === 0) return null;

  return (
    <div className="bg-gray-50 p-6 rounded-lg space-y-4 mt-8">
      <div>
        <h3 className="font-bold text-lg">台本</h3>
        <div className="space-y-3">
          {allScripts.map((script, index) => (
            <div
              key={index}
              className={`border-l-4 pl-4 ${
                script.source === "region"
                  ? "border-blue-300 bg-blue-50/50"
                  : "border-gray-200"
              }`}
            >
              <div className="flex items-center justify-between">
                <p
                  className={`font-medium ${
                    script.source === "region"
                      ? "text-blue-900"
                      : "text-gray-900"
                  }`}
                >
                  {script.speaker}
                </p>
                {script.source === "region" && (
                  <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                    選択範囲 {script.regionIndex}
                  </span>
                )}
              </div>
              <p
                className={`${
                  script.source === "region" ? "text-blue-800" : "text-gray-700"
                }`}
              >
                {script.lines}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
