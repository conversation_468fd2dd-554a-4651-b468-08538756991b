"use client";

import { Analysis } from "@/app/api/recognize/route";
import { SelectionRect } from "@/components/SelectionOverlay";

interface RegionAnalysis {
  analysis: Analysis;
  selection: SelectionRect;
  timestamp: number;
}

interface ResultViewerProps {
  analysis: Analysis | null;
  regionAnalyses?: RegionAnalysis[];
}

export default function ResultViewer({
  analysis,
  regionAnalyses = [],
}: ResultViewerProps) {
  if (!analysis && (!regionAnalyses || regionAnalyses.length === 0))
    return null;

  return (
    <div className="bg-gray-50 p-6 rounded-lg space-y-6 mt-8">
      {/* Full page analysis */}
      {analysis && (
        <div>
          <h3 className="font-bold text-lg">ページ全体の台本</h3>
          <div className="space-y-3">
            {analysis.scripts.map((script, index) => (
              <div key={index} className="border-l-4 border-gray-200 pl-4">
                <p className="font-medium text-gray-900">{script.speaker}</p>
                <p className="text-gray-700">{script.lines}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Region analyses */}
      {regionAnalyses.length > 0 && (
        <div>
          <h3 className="font-bold text-lg">選択範囲の台本</h3>
          <div className="space-y-4">
            {regionAnalyses.map((regionAnalysis, regionIndex) => (
              <div
                key={regionIndex}
                className="border border-blue-200 rounded-lg p-4 bg-blue-50"
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-blue-900">
                    選択範囲 {regionIndex + 1}
                  </h4>
                  <div className="text-xs text-blue-600">
                    座標: ({Math.round(regionAnalysis.selection.x)},{" "}
                    {Math.round(regionAnalysis.selection.y)}) サイズ:{" "}
                    {Math.round(regionAnalysis.selection.width)}×
                    {Math.round(regionAnalysis.selection.height)}
                  </div>
                </div>
                <div className="space-y-2">
                  {regionAnalysis.analysis.scripts.map(
                    (script, scriptIndex) => (
                      <div
                        key={scriptIndex}
                        className="border-l-4 border-blue-300 pl-4"
                      >
                        <p className="font-medium text-blue-900">
                          {script.speaker}
                        </p>
                        <p className="text-blue-800">{script.lines}</p>
                      </div>
                    )
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
