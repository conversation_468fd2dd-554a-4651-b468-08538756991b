"use client";

import { Analysis } from "@/app/api/recognize/route";

interface ResultViewerProps {
  analysis: Analysis | null;
}

export default function ResultViewer({ analysis }: ResultViewerProps) {
  if (!analysis) return null;

  return (
    <div className="bg-gray-50 p-6 rounded-lg space-y-4 mt-8">
      <div>
        <h3 className="font-bold text-lg">台本</h3>
        <div className="space-y-3">
          {analysis.scripts.map((script, index) => (
            <div key={index} className="border-l-4 border-gray-200 pl-4">
              <p className="font-medium text-gray-900">{script.speaker}</p>
              <p className="text-gray-700">{script.lines}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
