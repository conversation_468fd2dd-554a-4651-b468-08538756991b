"use client";

import { Analysis } from "@/app/api/recognize/route";
import React, { useState, useEffect } from "react";
import { X, GripVertical, GripHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface Script {
  speaker: string;
  lines: string;
}

interface ResultViewerProps {
  analysis: Analysis | null;
  onAnalysisUpdate?: (updatedAnalysis: Analysis) => void;
}

export default function ResultViewer({
  analysis,
  onAnalysisUpdate,
}: ResultViewerProps) {
  const [scripts, setScripts] = useState<Script[]>(analysis?.scripts || []);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  // Update local state when analysis changes
  useEffect(() => {
    setScripts(analysis?.scripts || []);
  }, [analysis]);

  if (!analysis) return null;

  // Function to wrap text at 24 characters
  const wrapText = (text: string, maxLength: number = 24): string => {
    if (text.length <= maxLength) return text;

    const lines = [];
    for (let i = 0; i < text.length; i += maxLength) {
      lines.push(text.slice(i, i + maxLength));
    }
    return lines.join("\n");
  };

  const handleDragStart = (
    e: React.DragEvent<HTMLDivElement>,
    index: number
  ) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/html", e.currentTarget.outerHTML);
    (e.currentTarget as HTMLElement).style.opacity = "0.5";
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    (e.currentTarget as HTMLElement).style.opacity = "1";
    setDraggedIndex(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const newScripts = [...scripts];
    const draggedScript = newScripts[draggedIndex];

    // Remove dragged item
    newScripts.splice(draggedIndex, 1);

    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newScripts.splice(insertIndex, 0, draggedScript);

    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ scripts: newScripts });
    }
  };

  const handleDelete = (index: number) => {
    const newScripts = scripts.filter((_, i) => i !== index);
    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ scripts: newScripts });
    }
  };

  const handleLinesChange = (index: number, newLines: string) => {
    // Remove existing line breaks and wrap at 24 characters
    const cleanText = newLines.replace(/\n/g, "");
    const wrappedText = wrapText(cleanText);

    const newScripts = [...scripts];
    newScripts[index] = { ...newScripts[index], lines: wrappedText };
    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ scripts: newScripts });
    }
  };

  return (
    <div className="bg-gray-50 p-6 rounded-lg space-y-4 mt-8">
      <div>
        <h3 className="font-bold text-lg">台本</h3>
        <ScrollArea className="rounded-md border bg-white whitespace-nowrap">
          <div className="flex flex-row-reverse w-max space-x-4 p-4 bg-white">
            {scripts.map((script, index) => (
              <Card
                key={index}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
                className="relative bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-move group flex-shrink-0"
                style={{
                  width: "auto",
                  minWidth: "60px",
                }}
              >
                <CardHeader>
                  <CardTitle
                    style={{
                      writingMode: "vertical-rl",
                      textOrientation: "upright",
                      direction: "ltr",
                    }}
                  >
                    {script.speaker}
                  </CardTitle>
                  <CardAction>
                    {/* Drag handle */}
                    <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <GripVertical className="w-4 h-4 text-gray-400" />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(index)}
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity w-6 h-6 p-0"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </CardAction>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={script.lines}
                    onChange={(e) => handleLinesChange(index, e.target.value)}
                    className="text-gray-700 text-sm leading-relaxed flex-1 resize-none border-none bg-transparent p-4 shadow-none focus-visible:ring-0 focus-visible:border-none whitespace-pre-line"
                    style={{
                      writingMode: "vertical-rl",
                      textOrientation: "upright",
                      minHeight: "150px",
                    }}
                  />
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(index)}
                    className="w-6 h-6 p-0　"
                  >
                    削除
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>
    </div>
  );
}
