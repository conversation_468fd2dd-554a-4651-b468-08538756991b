"use client";

import React, { useState, useRef, useCallback } from "react";

export interface SelectionRect {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface SelectionOverlayProps {
  isSelectionMode: boolean;
  onSelectionChange: (selection: SelectionRect | null) => void;
  onSelectionComplete: (selection: SelectionRect) => void;
  children: React.ReactNode;
  className?: string;
}

export default function SelectionOverlay({
  isSelectionMode,
  onSelectionChange,
  onSelectionComplete,
  children,
  className = "",
}: SelectionOverlayProps) {
  const [isSelecting, setIsSelecting] = useState(false);
  const [selection, setSelection] = useState<SelectionRect | null>(null);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  const getRelativeCoordinates = useCallback((e: React.MouseEvent) => {
    if (!overlayRef.current) return { x: 0, y: 0 };
    
    const rect = overlayRef.current.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }, []);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isSelectionMode) return;
    
    e.preventDefault();
    const coords = getRelativeCoordinates(e);
    setStartPoint(coords);
    setIsSelecting(true);
    setSelection(null);
    onSelectionChange(null);
  }, [isSelectionMode, getRelativeCoordinates, onSelectionChange]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isSelecting || !startPoint || !isSelectionMode) return;
    
    const coords = getRelativeCoordinates(e);
    const newSelection: SelectionRect = {
      x: Math.min(startPoint.x, coords.x),
      y: Math.min(startPoint.y, coords.y),
      width: Math.abs(coords.x - startPoint.x),
      height: Math.abs(coords.y - startPoint.y),
    };
    
    setSelection(newSelection);
    onSelectionChange(newSelection);
  }, [isSelecting, startPoint, isSelectionMode, getRelativeCoordinates, onSelectionChange]);

  const handleMouseUp = useCallback(() => {
    if (!isSelecting || !selection || !isSelectionMode) return;
    
    setIsSelecting(false);
    setStartPoint(null);
    
    // Only complete selection if it has meaningful dimensions
    if (selection.width > 10 && selection.height > 10) {
      onSelectionComplete(selection);
    } else {
      setSelection(null);
      onSelectionChange(null);
    }
  }, [isSelecting, selection, isSelectionMode, onSelectionComplete, onSelectionChange]);

  const clearSelection = useCallback(() => {
    setSelection(null);
    setIsSelecting(false);
    setStartPoint(null);
    onSelectionChange(null);
  }, [onSelectionChange]);

  // Clear selection when selection mode is disabled
  React.useEffect(() => {
    if (!isSelectionMode) {
      clearSelection();
    }
  }, [isSelectionMode, clearSelection]);

  return (
    <div
      ref={overlayRef}
      className={`relative ${className}`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      style={{
        cursor: isSelectionMode ? "crosshair" : "default",
      }}
    >
      {children}
      
      {/* Selection rectangle overlay */}
      {isSelectionMode && selection && (
        <div
          className="absolute border-2 border-blue-500 bg-blue-500/20 pointer-events-none"
          style={{
            left: selection.x,
            top: selection.y,
            width: selection.width,
            height: selection.height,
          }}
        >
          {/* Corner indicators */}
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full" />
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full" />
          <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full" />
          <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full" />
        </div>
      )}
      
      {/* Selection mode indicator */}
      {isSelectionMode && !selection && (
        <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-sm pointer-events-none">
          クリック&ドラッグで範囲を選択
        </div>
      )}
    </div>
  );
}
