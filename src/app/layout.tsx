import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "台本解析ツール(デモ)",
  description: "PDFや画像から台本を解析するツールのデモです。",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen p-8`}
      >
        <nav className="fixed top-0 left-0 w-full border-b shadow-sm z-50 bg-white">
          <div className="container mx-auto flex items-center justify-between h-14 px-4">
            <h1 className="text-2xl font-bold">台本解析ツール(デモ)</h1>
          </div>
        </nav>
        {children}
      </body>
    </html>
  );
}
