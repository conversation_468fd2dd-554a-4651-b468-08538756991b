"use client";

import ModelSelector from "@/components/ModelSelector";
import FileUploader from "@/components/FileUploader";
import MessageDisplay from "@/components/MessageDisplay";
import ResultViewer from "@/components/ResultViewer";
import ImageViewer from "@/components/ImageViewer";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Script } from "@/app/api/recognize/route";
import { MODELS } from "@/lib/const";

export default function Home() {
  const [file, setFile] = useState<File | null>(null);
  const [analysis, setAnalysis] = useState<Script[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRegionAnalyzing, setIsRegionAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [model, setModel] = useState(MODELS.OpenAI[0].id);
  const [pdfImages, setPdfImages] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [isLoadingImages, setIsLoadingImages] = useState(false);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] ?? null;
    if (file && file.type === "application/pdf") {
      setFile(file);
      setError(null);
      setAnalysis([]);
      setPdfImages([]);
      setCurrentPage(0);

      // Convert PDF to images
      setIsLoadingImages(true);
      try {
        const formData = new FormData();
        formData.append("file", file);

        const response = await fetch("/api/pdf-to-images", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error("PDF画像変換に失敗しました");
        }

        const data = await response.json();
        setPdfImages(data.images);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "PDF変換エラーが発生しました"
        );
        setPdfImages([]);
      } finally {
        setIsLoadingImages(false);
      }
    } else {
      setError("PDFファイルを選択してください");
      setFile(null);
      setPdfImages([]);
      setAnalysis([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (pdfImages.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("model", model);
      formData.append("image", pdfImages[currentPage]);

      const response = await fetch("/api/recognize", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("ファイルの解析に失敗しました");
      }

      const data = await response.json();
      setAnalysis((prev) => {
        prev[currentPage] = data.analysis;
        return prev;
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleAnalysisUpdate = (updatedAnalysis: Script) => {
    setAnalysis((prev) => {
      const newAnalysis = [...prev];
      newAnalysis[currentPage] = updatedAnalysis;
      return newAnalysis;
    });
  };

  const handleRegionAnalyze = async (croppedImageData: string) => {
    setIsRegionAnalyzing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("model", model);
      formData.append("image", croppedImageData);

      const response = await fetch("/api/recognize", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("選択範囲の解析に失敗しました");
      }

      const data = await response.json();

      // Append the region analysis paragraphs to the current page analysis
      setAnalysis((prev) => {
        const newAnalysis = [...prev];
        if (!newAnalysis[currentPage]) {
          newAnalysis[currentPage] = { paragraphs: [] };
        }

        // Append region analysis paragraphs to existing paragraphs
        newAnalysis[currentPage] = {
          paragraphs: [
            ...newAnalysis[currentPage].paragraphs,
            ...data.analysis.paragraphs,
          ],
        };

        return newAnalysis;
      });
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "選択範囲の解析でエラーが発生しました"
      );
    } finally {
      setIsRegionAnalyzing(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex flex-col items-center gap-6">
          <ModelSelector model={model} setModel={setModel} />

          <FileUploader file={file} handleFileChange={handleFileChange} />

          <Button
            type="submit"
            disabled={pdfImages.length === 0 || isLoading || isLoadingImages}
            className="w-full max-w-md"
          >
            {isLoading
              ? "解析中..."
              : isLoadingImages
              ? "PDF変換中..."
              : "ファイルを解析"}
          </Button>
        </div>
      </form>

      <MessageDisplay error={error} />

      {pdfImages.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <ImageViewer
              images={pdfImages}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              onRegionAnalyze={handleRegionAnalyze}
              isAnalyzing={isRegionAnalyzing}
            />
          </div>
          <div>
            <ResultViewer
              analysis={analysis[currentPage]}
              onAnalysisUpdate={handleAnalysisUpdate}
            />
          </div>
        </div>
      )}
    </div>
  );
}
