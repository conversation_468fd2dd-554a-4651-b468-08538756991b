# テレビ番組台本の手書き加筆テキスト抽出タスク

## 目的

テレビ番組の台本に手書きで加筆された画像からテキストを抽出し、構造化された JSON データに変換する。

## 入力情報

- 画像には縦書き（右から左）のテキストが含まれています
- 話者名はセリフの最初の行の上にスペースを開けて書かれています
- セリフには手書きの加筆や取り消し線が含まれる場合があります

## 処理手順

1. 画像内のすべてのテキストを認識してください
2. 話者名とセリフを識別してください
3. 取り消し線がついているテキストは除外してください
4. 以下の JSON フォーマットで結果を出力してください

## 注意事項

- 話者名が書かれていない場合は、空の文字列として出力すること。
- セリフは省略せず、すべての文字を正確に認識して出力すること。
- 番組ジャンルや文法を考慮した上で、自然な文章になっているかを確認すること。

## 出力フォーマット

```json
{
  "scripts": [
    {
      "speaker": "話者名（例：佐藤アナ、山田太郎、静など）",
      "lines": "セリフ内容（取り消し線部分は除外）"
    },
    {
      "speaker": "次の話者名",
      "lines": "次のセリフ内容"
    }
  ]
}
```
