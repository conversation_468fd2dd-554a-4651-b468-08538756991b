# 歌舞伎の手書き台本の手書きテキスト抽出タスク

## 目的

手書きの台本画像からテキストを抽出し、構造化された JSON データに変換する。

## 入力情報

- 画像には縦書き（右から左）の手書きテキストが含まれている。
- 画像には、台詞、歌詞、ト書きの 3 種類の段落が含まれている。

- **台詞**: 登場人物ののセリフ。セリフの最初の行の上に話者名が書かれている。
- **歌詞**: 短歌や謡曲の歌詞。段落が 1 段下がっており、平仮名の「へ」に似た山が 2 つの記号「〽」(庵点)が付いている。
- **ト書き**: 人物の動作や表情など、場面の状況説明。段落が 2 段下がっており、「ト、」で始まる。

## 処理手順

1. 画像内のすべてのテキストを認識
2. 段落ごとに種類を分類
3. 台詞の場合は話者名を特定
4. 取り消し線がついているテキストは除外
5. 追記されたテキストを反映
6. 以下の JSON フォーマットで結果を出力

## 注意事項

- 省略せず、すべての文字を正確に認識して出力すること。
- 古文の文法を考慮した上で、自然な文章になっているかを確認すること。

## 出力フォーマット

```json
{
  "paragraphs": [
    {
      "type": "段落の種類(台詞、歌詞、ト書き)",
      "header": "（セリフの場合は話者名、歌詞の場合は「〽」、ト書きの場合は「ト、」）",
      "content": "セリフ内容（取り消し線部分は除外、追記された部分も反映）"
    }
  ]
}
```
